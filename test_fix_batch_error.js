import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFix() {
  console.log('Testing the batch processing fix...');
  
  // Reset task 300166 to pending status to test the fix
  console.log('Resetting task 300166 to pending status...');
  const { error: resetError } = await supabase
    .from('t_task_queue')
    .update({
      status: 'pending',
      result: null,
      processed_at: null,
      locked_at: null,
      locked_by: null,
      scheduled_at: new Date().toISOString()
    })
    .eq('id', 300166);
    
  if (resetError) {
    console.error('Error resetting task:', resetError.message);
    return;
  }
  
  console.log('Task 300166 reset to pending. You can now run the worker to test the fix.');
  
  // Check the task status
  const { data: task, error: checkError } = await supabase
    .from('t_task_queue')
    .select('id, status, payload, scheduled_at')
    .eq('id', 300166)
    .single();
    
  if (checkError) {
    console.error('Error checking task:', checkError.message);
  } else {
    console.log('Task status:', task);
  }
}

testFix();
